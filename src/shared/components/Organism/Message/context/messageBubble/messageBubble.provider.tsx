'use client';

import useCreateContext from 'shared/utils/hooks/useCreateContext';
import type { State } from './messageBubble.reducer';
import messageBubbleReducer from './messageBubble.reducer';

// Create the initial state
const initialState: State = {
  message: {} as any,
  sectionName: undefined,
};

// Use the useCreateContext utility to create separated contexts
// eslint-disable-next-line react-hooks/rules-of-hooks
const [state, dispatch, provider] = useCreateContext(
  messageBubbleReducer,
  initialState
);

export const useMessageBubbleState = state;
export const useMessageBubbleDispatch = dispatch;
export const MessageBubbleProvider = provider;

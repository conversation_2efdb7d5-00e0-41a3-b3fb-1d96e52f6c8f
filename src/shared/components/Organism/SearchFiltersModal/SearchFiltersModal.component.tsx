import React from 'react';
import useMedia from 'shared/uikit/utils/useMedia';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from 'shared/utils/hooks/useTranslation';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import type {
  SearchFiltersQueryParamsType,
  SearchFiltersValueType,
} from 'shared/types/search';
import useSearchFilters from 'shared/hooks/useSearchFilters';
import useSearchFiltersFields from 'shared/hooks/useSearchFiltersFields';
import { sleep } from 'shared/utils/toolkit/sleep';
import classes from './SearchFiltersModal.component.module.scss';
import SearchFiltersForm from './SearchFiltersForm';

export interface SearchFiltersModalProps {
  onClose: () => void;
  groups?: any;
  customSetResult?: Function;
  justUseCustom?: boolean;
  isBusinessApp?: boolean;
}

const SearchFiltersModal: React.FC<SearchFiltersModalProps> = ({
  onClose,
  groups: parentGroup,
  customSetResult,
  justUseCustom = false,
  isBusinessApp,
}) => {
  const { isTabletAndLess } = useMedia();
  const { t } = useTranslation();
  const { resetFilters, addToDynamicFiltersAndSetFilter } = useSearchFilters();
  const { groups } = useSearchFiltersFields();

  const onCloseHandler = () => {
    onClose();
  };

  const onClickOutside = () => {
    if (isTabletAndLess) return;
    onCloseHandler();
  };

  const onSuccessHandler = async (
    variables: Record<SearchFiltersQueryParamsType, SearchFiltersValueType>
  ) => {
    const values = Object.keys(variables)?.reduce((acc, key) => {
      const value = variables?.[key];
      if (!hasValue(value)) return acc;
      acc[key] = value;
      return acc;
    }, {});
    onCloseHandler();
    if (!justUseCustom) {
      addToDynamicFiltersAndSetFilter({
        ...values,
        [searchFilterQueryParams.searchGroupType]: searchGroupTypes.ALL,
      });
    }
    if (customSetResult) {
      await sleep(50);
      customSetResult(values);
    }
  };

  const resetFormHandler = () => {
    resetFilters();
    onCloseHandler();
  };
  console.log('=========');

  // return null;

  return (
    <FixedRightSideModalDialog
      onBack={onCloseHandler}
      onClose={onCloseHandler}
      onClickOutside={onClickOutside}
      visibleBackdrop
      contentClassName="!max-w-full"
      {...(isBusinessApp && {
        wide: true,
        fullBackdrop: true,
        doubleColumn: true,
      })}
    >
      <ModalHeaderSimple
        backButtonProps={{ onClick: onCloseHandler }}
        closeButtonProps={{ onClick: onCloseHandler }}
        className={classes.header}
        visibleHeaderDivider={isTabletAndLess}
        title={t('all_filters')}
      />
      <SearchFiltersForm
        groups={parentGroup ?? groups}
        local
        onSuccess={onSuccessHandler}
        enableReinitialize
        onReset={resetFormHandler}
        isBusinessApp={isBusinessApp}
      />
    </FixedRightSideModalDialog>
  );
};

export default SearchFiltersModal;

function hasValue(value: string | any[]) {
  return Array.isArray(value) ? Object.keys(value)?.length > 0 : !!value;
}

import React, { createContext, useState } from 'react';
import { useMultiStepFormState } from 'shared/hooks/useMultiStepForm';
import CreatePageFormMultiStepForm from 'shared/components/Organism/MultiStepForm/CreatePageForm/CreatePageFormMultiStepForm';
import type * as MODAL_KEYS from '../constants/profileModalsKeys';

export type ModalKeys = keyof typeof MODAL_KEYS;

export type ModalStateType = {
  active?: ModalKeys;
  selectedForEdit?: ModalKeys;
  prev: Array<ModalKeys>;
  params?: { [key: string]: any };
};

// Separate contexts for state and actions
export const ProfileModalsStateContext = createContext<ModalStateType | undefined>(undefined);

export const ProfileModalsActionsContext = createContext<{
  setSelectedForEdit: (modalKey: ModalKeys) => void;
} | undefined>(undefined);

const INIT_STATE = {
  active: undefined,
  selectedForEdit: undefined,
  prev: [],
  params: {},
};

const useProfileModals = () => {
  const [modals, setModals] = useState<ModalStateType>(INIT_STATE);

  const setSelectedForEdit = (modalKey: ModalKeys) => {
    setModals((prevState: ModalStateType) => ({
      ...prevState,
      selectedForEdit: modalKey,
    }));
  };

  return {
    modals,
    setSelectedForEdit,
  };
};

interface ProfileModalsProviderProps {
  children?: ReactNode;
  userName?: string;
  onSuccessCallback?: (data: never) => void;
  onCloseCallback?: () => void;
}

const ProfileModalsProvider: React.FC<ProfileModalsProviderProps> = ({
  children,
  userName,
  onSuccessCallback,
  onCloseCallback,
}) => {
  const modalProps = useProfileModals();
  const createPageForm = useMultiStepFormState('createPageForm');

  return (
    <ProfileModalsContext.Provider value={modalProps}>
      {children}
      {createPageForm?.isOpen && (
        <CreatePageFormMultiStepForm
          userName={userName}
          onSuccessCallback={onSuccessCallback}
          onCloseCallback={onCloseCallback}
        />
      )}
    </ProfileModalsContext.Provider>
  );
};

export default ProfileModalsProvider;
